<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;

class DevelopmentServiceProvider extends ServiceProvider
{
    /**
     * Register services for development environment only.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment('local')) {
            // Only register heavy services when explicitly needed
            if (config('telescope.enabled', false)) {
                $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            }
            
            // Register development-only services
            if (class_exists(\Barryvdh\Debugbar\ServiceProvider::class) && config('debugbar.enabled', false)) {
                $this->app->register(\Barryvdh\Debugbar\ServiceProvider::class);
            }
        }
    }

    /**
     * Bootstrap services for development environment.
     *
     * @return void
     */
    public function boot()
    {
        if ($this->app->environment('local')) {
            // Development-specific optimizations
            $this->optimizeForDevelopment();
        }
    }

    /**
     * Apply development-specific optimizations.
     *
     * @return void
     */
    private function optimizeForDevelopment()
    {
        // Disable unnecessary features in development
        if (!config('app.debug_performance', true)) {
            // Disable query logging to save memory
            \DB::connection()->disableQueryLog();
        }

        // Set development-specific memory limits
        ini_set('memory_limit', config('app.dev_memory_limit', '512M'));
        
        // Optimize garbage collection for development
        if (function_exists('gc_enable')) {
            gc_enable();
        }
        
        // Set development-specific timeouts
        set_time_limit(config('app.dev_max_execution_time', 300));
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
