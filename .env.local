APP_NAME=DMM
APP_ENV=local
APP_KEY=base64:dy/0gCmVFBVTVopcroiXo+PuL64y7U2vsNtbQiWDSGE=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

LOG_CHANNEL=single
LOG_LEVEL=error

# Development Performance Optimizations
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
VIEW_CACHE_ENABLED=false
CONFIG_CACHE_ENABLED=false
ROUTE_CACHE_ENABLED=false

# Disable heavy features for development
MAIL_MAILER=log
BROADCAST_DRIVER=log

RECAPTCHA_SITE_KEY=YOUR_API_SITE_KEY
RECAPTCHA_SECRET_KEY=YOUR_API_SECRET_KEY

DB_CONNECTION=pgsql
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=db_dmm
DB_USERNAME=dmm_user
DB_PASSWORD=sisidmm

BROADCAST_DRIVER=log
CACHE_DRIVER=array
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Disable SAP features for local development
SAP_ENABLED=false
SAP_RFC_ENABLED=false

# SAP ACCOUNT  SIG - PRODUCTION
#SAP_ASHOST=**********
SAP_SYSNR=20
SAP_CLIENT=210
SAP_USER=rfcdmm
SAP_PASSWD=Rfcdmm@2022
SAP_MSHOST="sepappvip.sig.id"
SAP_GROUP="SAPRFC4"
SAP_R3NAME="SEP"

# SAP ACCOUNT SBI - PROD
#SBI_SAP_ASHOST=*************
SBI_SAP_ASHOST=***********
SBI_SAP_SYSNR=00
SBI_SAP_CLIENT=400
SBI_SAP_USER=RAUTEXT03
SBI_SAP_PASSWD=dmmsbi2022

#SSO LOGIN
#SSO_URL="http://************:8000/api/login"
SSO_URL="https://sso.sig.id/api/login"
SSO_TOKEN="3dd6aa95cb3d28c7f553d756d9b51263"

ALLOWED_REFERER = "127.0.0.1:8000;devoprex.sig.id;www.oprex.sig.id;oprex.sig.id;localhost:8000;dev-dmm.test;"