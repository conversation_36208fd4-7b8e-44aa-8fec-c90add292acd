<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Models\Routes;

// Essential authentication routes (must be defined first)
Route::get('/', 'LoginController@index');
Route::get('/login', 'LoginController@index')->name('login');
Route::post('/login/store', 'LoginController@store')->name('login.store');
Route::get('/logout', 'LoginController@destroy')->name('logout');

// Debug route to test login functionality
Route::get('/test-login', function() {
    return 'Login routes are working. Please use the login form to submit to /login/store';
});

// Test route to verify named routes work
Route::get('/test-named-route', function() {
    try {
        $loginUrl = route('login');
        $loginStoreUrl = route('login.store');
        return "Login URL: $loginUrl<br>Login Store URL: $loginStoreUrl<br>Named routes are working!";
    } catch (Exception $e) {
        return "Named route error: " . $e->getMessage();
    }
});

// API test routes
Route::get('/api/tesmail/send/{to}', 'Tesmail@send');
Route::get('/api/tesmail/', 'Tesmail@index');
Route::post('/api/tesmail/eq', 'Tesmail@EQ');
Route::get('/api/tesmail/get_temp', 'Tesmail@get_temp');
Route::get('/api/tesmail/cenv', 'Tesmail@Cenv');

// Dynamic routes from database (with better error handling)
try {
    $routes = Routes::where('guard', 'web')->get()->toArray();

    Route::middleware(['prevent-back-history', 'sql', 'setlang'])->group(function () use ($routes) {
        foreach ($routes as $key) {
            # code...

            $arr = explode(',', $key['middleware']);
            $method = $key['method'];
            $url = $key['url'];
            $path = $key['route'];
            if ($key['permission'] != '') {
                # code...
                $perm = "permissionAccess:";
                if ($key['type'] != 'data') {
                    $perm = 'permission:';
                }
                array_push($arr, $perm . $key['permission']);
            }
            
            if (strpos($url, '{id}') !== false) {
                Route::$method($url, $path)->where('id', '[0-9]+')->middleware($arr);
            } else {
                Route::$method($url, $path)->middleware($arr);
            }
        }
    });
} catch (Exception $e) {
    // Log the error but don't break the application
    \Log::warning('Failed to load dynamic routes from database: ' . $e->getMessage());

    // You can add fallback routes here if needed
    // Route::get('/dashboard', 'DashboardController@index')->middleware('authz');
}
