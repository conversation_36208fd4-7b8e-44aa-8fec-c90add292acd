{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --config=node_modules/laravel-mix/setup/webpack.config.js", "dev-fast": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --mode=development --config=node_modules/laravel-mix/setup/webpack.config.js", "clear-cache": "rm -rf node_modules/.cache && rm -rf public/js && rm -rf public/css", "optimize": "npm run clear-cache && npm run dev"}, "devDependencies": {"axios": "^0.19", "cross-env": "^7.0", "laravel-mix": "^5.0.1", "lodash": "^4.17.13", "resolve-url-loader": "^3.1.0", "sass": "^1.15.2", "sass-loader": "^8.0.0", "vue-template-compiler": "^2.6.14", "browser-sync": "^2.26.7", "browser-sync-webpack-plugin": "^2.2.2"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=12.0.0", "npm": ">=6.0.0"}}