<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Development Cache Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration is optimized for development environments to provide
    | fast caching with minimal overhead and easy cache clearing.
    |
    */

    'default' => env('CACHE_DRIVER', 'array'),

    'stores' => [

        'array' => [
            'driver' => 'array',
            'serialize' => false, // Faster for development
        ],

        'file' => [
            'driver' => 'file',
            'path' => storage_path('framework/cache/data'),
        ],

        // Redis configuration for development (optional)
        'redis' => [
            'driver' => 'redis',
            'connection' => 'cache',
            'lock_connection' => 'default',
        ],

        // Memory-based cache for development
        'memory' => [
            'driver' => 'array',
            'serialize' => false,
        ],

        // Null cache for testing
        'null' => [
            'driver' => 'null',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    |
    | When utilizing a RAM based store such as APC or Memcached, there might
    | be other applications utilizing the same cache. So, we'll specify a
    | value to get prefixed to all our keys so we can avoid collisions.
    |
    */

    'prefix' => env('CACHE_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_dev_cache'),

];
