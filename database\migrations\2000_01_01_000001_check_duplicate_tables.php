<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CheckDuplicateTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // This migration runs early to prevent duplicate table issues
        // It doesn't create tables, just ensures consistency
        
        // List of tables that might be created multiple times
        $potentialDuplicates = [
            'm_opco',
            'sync_opc_configs', 
            'm_plant_ics',
            'ts_realisasi_performance_daily',
            't_realisasi_penjualan',
            'ts_realisasi_capex'
        ];
        
        foreach ($potentialDuplicates as $table) {
            if (Schema::hasTable($table)) {
                // Log to Laravel log instead of command output
                \Log::warning("Table {$table} already exists - later migrations should check for existence");
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Nothing to rollback
    }
}
