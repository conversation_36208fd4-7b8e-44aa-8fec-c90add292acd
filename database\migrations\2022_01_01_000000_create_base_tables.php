<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBaseTables extends Migration
{
    public function up()
    {
        // Create m_opco table with all required columns
        if (!Schema::hasTable('m_opco')) {
            Schema::create('m_opco', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_opco', 30)->nullable();
                $table->string('name_opco', 100)->nullable();
                $table->integer('no_pbi')->nullable();
                $table->string('created_by', 30)->nullable();
                $table->string('updated_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Create sync_opc_configs if missing
        if (!Schema::hasTable('sync_opc_configs')) {
            Schema::create('sync_opc_configs', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('name')->nullable();
                $table->string('type')->nullable();
                $table->string('url')->nullable();
                $table->text('parameter')->nullable();
                $table->enum('schedule', ['minutely', 'everyfiveminute', 'hourly', 'daily', 'monthly'])->nullable();
                $table->integer('at_date')->nullable();
                $table->time('at_time')->nullable();
                $table->enum('status', ['active', 'non_active'])->nullable();
                $table->string('config_name', 100)->nullable();
                $table->text('config_value')->nullable();
                $table->boolean('is_active')->default(true);
                $table->string('kode_opco', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        Schema::create('m_plant_ics', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('kode_plant', 30)->nullable();
            $table->string('name_plant', 100)->nullable();
            $table->string('kode_opco', 30)->nullable();
            $table->string('created_by', 30)->nullable();
            $table->string('updated_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('ts_realisasi_performance_daily', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable();
            $table->float('oph', 8, 5);
            $table->float('updt', 8, 5);
            $table->float('pdt', 8, 5);
            $table->float('stop_idle', 8, 5);
            $table->float('fy_stop', 8, 5);
            $table->float('frek_updt', 8, 5);
            $table->string('problem', 200);
            $table->string('source_system', 30)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->string('kode_opco', 30)->nullable();
            $table->float('rate_gross', 52, 0)->nullable();
            $table->float('rate_netto', 52, 0)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('t_realisasi_penjualan', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('kode_opco', 30)->nullable();
            $table->date('tanggal')->nullable();
            $table->decimal('volume_penjualan', 15, 2)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Create ts_realisasi_capex with all required columns
        if (!Schema::hasTable('ts_realisasi_capex')) {
            Schema::create('ts_realisasi_capex', function (Blueprint $table) {
                $table->bigIncrements('id'); // or id_realisasi_capex if needed
                $table->string('kode_opco', 30);
                $table->date('tanggal')->nullable();
                $table->float('real_capex', 52, 0)->nullable(); // or bigInteger
                $table->string('create_by', 30)->nullable();
                $table->string('update_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('ts_realisasi_capex');
        Schema::dropIfExists('t_realisasi_penjualan');
        Schema::dropIfExists('ts_realisasi_performance_daily');
        Schema::dropIfExists('m_plant_ics');
        Schema::dropIfExists('sync_opc_configs');
        Schema::dropIfExists('m_opco');
    }
}
