<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class DataPenilaianOpcoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        //
        // Instuksi Untuk Running Penilaian Quisioner
        // 1. Create <PERSON><PERSON>
        // 2. Create permision data-penilaian-opco
        // 3. php artisan migrate --path=/database/migrations/2025_09_05_001503_create_dmm_data_penilaian_opco_table.php
        // 4. php artisan db:seed --class=DataPenilaianOpcoSeeder

        $data = [
            ['permission' => 'data-penilaian-opco-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/data-penilaian-opco', 'route' => 'Penilaian\DataPenilaianOpcoController@index', 'guard' => 'web'],
            ['permission' => 'data-penilaian-opco-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/data-penilaian-opco-datatable', 'route' => 'Penilaian\DataPenilaianOpcoController@datatables', 'guard' => 'web'],
            ['permission' => 'data-penilaian-opco-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/data-penilaian-opco/{uuid}', 'route' => 'Penilaian\DataPenilaianOpcoController@show', 'guard' => 'web'],
            ['permission' => 'data-penilaian-opco-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/data-penilaian-opco', 'route' => 'Penilaian\DataPenilaianOpcoController@store', 'guard' => 'web'],
            ['permission' => 'data-penilaian-opco-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/data-penilaian-opco-store-assessment', 'route' => 'Penilaian\DataPenilaianOpcoController@storeAssessment', 'guard' => 'web'],
            ['permission' => 'data-penilaian-opco-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/data-penilaian-opco/{uuid}/delete', 'route' => 'Penilaian\DataPenilaianOpcoController@destroy', 'guard' => 'web'],
            ['permission' => 'data-penilaian-opco-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/data-penilaian-opco/{uuid}/update', 'route' => 'Penilaian\DataPenilaianOpcoController@update', 'guard' => 'web'],
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
        // DON'T DO , i's only for emergency or development
        // php artisan migrate:rollback --path=/database/migrations/2025_09_05_001503_create_dmm_data_penilaian_opco_table.php
    }
}
