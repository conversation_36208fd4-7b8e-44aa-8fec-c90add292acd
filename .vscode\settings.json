{
  "postman.settings.dotenv-detection-notification-visibility": false,

  // Performance optimizations
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/vendor/**": true,
    "**/storage/logs/**": true,
    "**/storage/framework/cache/**": true,
    "**/storage/framework/sessions/**": true,
    "**/storage/framework/views/**": true,
    "**/bootstrap/cache/**": true,
    "**/.git/**": true,
    "**/public/js/**": true,
    "**/public/css/**": true,
    "**/public/mix-manifest.json": true
  },

  "search.exclude": {
    "**/node_modules": true,
    "**/vendor": true,
    "**/storage/logs": true,
    "**/storage/framework": true,
    "**/bootstrap/cache": true,
    "**/public/js": true,
    "**/public/css": true
  },

  "files.exclude": {
    "**/storage/logs": true,
    "**/storage/framework/cache": true,
    "**/storage/framework/sessions": true,
    "**/storage/framework/views": true,
    "**/bootstrap/cache": true
  },

  // PHP optimizations
  "php.validate.enable": true,
  "php.validate.executablePath": "php",
  "php.suggest.basic": false,

  // Laravel-specific settings
  "blade.format.enable": true,
  "emmet.includeLanguages": {
    "blade": "html"
  },

  // Editor performance
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": false
  },
  "editor.suggestOnTriggerCharacters": true,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.tabCompletion": "on",

  // File associations
  "files.associations": {
    "*.blade.php": "blade",
    ".env*": "dotenv"
  },

  // Auto-save for faster development
  "files.autoSave": "onFocusChange",
  "files.autoSaveDelay": 1000,

  // Terminal optimizations
  "terminal.integrated.shell.windows": "bash.exe",
  "terminal.integrated.shellArgs.windows": ["--login"],

  // Git optimizations
  "git.autofetch": false,
  "git.autorefresh": false,

  // Disable heavy features for better performance
  "extensions.autoUpdate": false,
  "telemetry.enableTelemetry": false,
  "workbench.enableExperiments": false,

  // Language server optimizations
  "typescript.disableAutomaticTypeAcquisition": true,
  "javascript.suggest.autoImports": false
}