<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class DetailKriteriaPenilaianSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        // Instuksi Untuk Running Detail Kriteria Penilaian
        // 1. Create <PERSON><PERSON>
        // 2. Create permision kriteria-penilaian
        // 3. php artisan migrate --path=/database/migrations/2025_09_03_163057_create_dmm_detail_kriteria_penilaian_table.php
        // 4. php artisan db:seed --class=DetailKriteriaPenilaianSeeder

        $data = [
            ['permission' => 'kriteria-penilaian-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/kriteria-penilaian', 'route' => 'Master\DetailKriteriaPenilaianController@index', 'guard' => 'web'],
            ['permission' => 'kriteria-penilaian-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/kriteria-penilaian-datatable', 'route' => 'Master\DetailKriteriaPenilaianController@datatables', 'guard' => 'web'],
            ['permission' => 'kriteria-penilaian-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/kriteria-penilaian/{uuid}', 'route' => 'Master\DetailKriteriaPenilaianController@show', 'guard' => 'web'],
            ['permission' => 'kriteria-penilaian-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/kriteria-penilaian', 'route' => 'Master\DetailKriteriaPenilaianController@store', 'guard' => 'web'],
            ['permission' => 'kriteria-penilaian-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/kriteria-penilaian/{uuid}/update', 'route' => 'Master\DetailKriteriaPenilaianController@update', 'guard' => 'web'],
            ['permission' => 'kriteria-penilaian-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/kriteria-penilaian/{uuid}/delete', 'route' => 'Master\DetailKriteriaPenilaianController@destroy', 'guard' => 'web'],
          
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
    }
}
