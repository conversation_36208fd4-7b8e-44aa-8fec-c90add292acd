<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class MasterPeriodePenilaianSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        // Instuksi Untuk Running Master Quisioner
        // 1. Create <PERSON><PERSON> di <PERSON>
        // 2. Create permision periode-penilaian
        // 3. php artisan migrate --path=/database/migrations/2025_09_03_094440_create_dmm_periode_penilaian_table.php
        // 4. php artisan db:seed --class=MasterPeriodePenilaianSeeder

        $data = [
            ['permission' => 'periode-penilaian-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/periode-penilaian', 'route' => 'Master\PeriodePenilaianController@index', 'guard' => 'web'],
            ['permission' => 'periode-penilaian-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/periode-penilaian-datatable', 'route' => 'Master\PeriodePenilaianController@datatables', 'guard' => 'web'],
            ['permission' => 'periode-penilaian-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/periode-penilaian/{uuid}', 'route' => 'Master\PeriodePenilaianController@show', 'guard' => 'web'],
            ['permission' => 'periode-penilaian-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/periode-penilaian', 'route' => 'Master\PeriodePenilaianController@store', 'guard' => 'web'],
            ['permission' => 'periode-penilaian-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/periode-penilaian/{uuid}/update', 'route' => 'Master\PeriodePenilaianController@update', 'guard' => 'web'],
            ['permission' => 'periode-penilaian-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/periode-penilaian/{uuid}/delete', 'route' => 'Master\PeriodePenilaianController@destroy', 'guard' => 'web'],
          
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
    }
}
