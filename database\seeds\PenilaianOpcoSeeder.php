<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class PenilaianOpcoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        // Instuksi Untuk Running Master Quisioner
        // 1. Create <PERSON><PERSON> di <PERSON>
        // 2. Create permision penilaian-opco
        // 3. php artisan migrate --path=/database/migrations/2025_09_03_181044_create_dmm_penilaian_opco_table.php
        // 4. php artisan db:seed --class=PenilaianOpcoSeeder

        $data = [
            ['permission' => 'penilaian-opco-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/penilaian-opco', 'route' => 'Master\PenilaianOpcoController@index', 'guard' => 'web'],
            ['permission' => 'penilaian-opco-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/penilaian-opco-datatable', 'route' => 'Master\PenilaianOpcoController@datatables', 'guard' => 'web'],
            ['permission' => 'penilaian-opco-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/penilaian-opco/{uuid}', 'route' => 'Master\PenilaianOpcoController@show', 'guard' => 'web'],
            ['permission' => 'penilaian-opco-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/penilaian-opco', 'route' => 'Master\PenilaianOpcoController@store', 'guard' => 'web'],
            ['permission' => 'penilaian-opco-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/penilaian-opco/{uuid}/delete', 'route' => 'Master\PenilaianOpcoController@destroy', 'guard' => 'web'],
            ['permission' => 'penilaian-opco-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/penilaian-opco/{uuid}/update', 'route' => 'Master\PenilaianOpcoController@update', 'guard' => 'web'],
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
        // DON'T DO , i's only for emergency or development
        // php artisan migrate:rollback --path=/database/migrations/2025_09_03_181044_create_dmm_penilaian_opco_table.php
    }
}
