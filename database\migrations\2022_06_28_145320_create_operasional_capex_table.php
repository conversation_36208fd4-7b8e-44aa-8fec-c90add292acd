<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOperasionalCapexTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('operasional_capex', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('company_id');
            $table->integer('type_investment')->nullable();
            $table->string('profile_projects')->nullable();
            $table->string('coor_invest')->nullable();
            $table->string('asset_code')->nullable();
            $table->string('project_inisiator')->nullable();
            $table->string('cost_center')->nullable();
            $table->string('investment_name')->nullable();
            $table->string('existing_condition')->nullable();
            $table->string('capex_success_indicator')->nullable();
            $table->string('detail_scope')->nullable();
            $table->string('priority')->nullable();
            $table->year('year')->nullable();
            $table->string('intiator')->nullable();
            $table->string('kadept_initiator')->nullable();
            $table->string('focus_strategy')->nullable();
            $table->string('risk_if_not_done')->nullable();
            $table->string('invest_group')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('investment')->nullable();
            $table->string('consequences')->nullable();
            $table->string('likelihood')->nullable();
            $table->string('level')->nullable();
            $table->string('invesment_status')->nullable();
            $table->string('planning_commitment')->nullable();
            $table->string('po')->nullable();
            $table->string('next_year_po_1')->nullable();
            $table->string('next_year_po_2')->nullable();
            $table->string('next_year_po_3')->nullable();
            $table->string('planning_good_receipt')->nullable();
            $table->string('gr')->nullable();
            $table->string('no_equipment')->nullable();
            $table->string('depreiasi')->nullable();
            $table->string('quantity')->nullable();
            $table->string('nilai_barang_dan_jasa_import')->nullable();
            $table->string('nilai_barang_lokal')->nullable();
            $table->string('nilai_jasa')->nullable();
            $table->string('eng_mngmt_cost')->nullable();
            $table->string('total_investment')->nullable();
            $table->string('komponen_tkdn')->nullable();
            $table->string('benefit_analysis')->nullable();
            $table->string('similar_capex_reference')->nullable();
            $table->string('wbs')->nullable();
            $table->string('supporting_documents')->nullable();
            $table->string('price_list_OEM')->nullable();
            $table->string('engineering_judgement')->nullable();
            $table->string('critical_part')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('operasional_capex');
    }
}
