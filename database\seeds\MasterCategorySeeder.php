<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class MasterCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        // Instuksi Untuk Running Master Category
        // 1. Create <PERSON><PERSON> di Development
        // 2. Create permision master-category
        // 3. php artisan migrate --path=/database/migrations/2025_09_03_155857_create_m_category_table.php
        // 4. php artisan db:seed --class=MasterCategorySeeder

        $data = [
            ['permission' => 'master-category-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/master-category', 'route' => 'MasterCategoryController@index', 'guard' => 'web'],
            ['permission' => 'master-category-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/master-category/list', 'route' => 'MasterCategoryController@datatables', 'guard' => 'web'],
            ['permission' => 'master-category-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/master-category/{id}', 'route' => 'MasterCategoryController@show', 'guard' => 'web'],
            ['permission' => 'master-category-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/master-category', 'route' => 'MasterCategoryController@store', 'guard' => 'web'],
            ['permission' => 'master-category-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/master-category/{id}/update', 'route' => 'MasterCategoryController@update', 'guard' => 'web'],
            ['permission' => 'master-category-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/master-category/{id}/delete', 'route' => 'MasterCategoryController@destroy', 'guard' => 'web'],
          
        ];

        foreach($data as $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");

        // permisions
        
    }
}
