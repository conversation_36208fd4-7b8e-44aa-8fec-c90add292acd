<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DevPerformanceMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:performance {--clear : Clear performance cache}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor development environment performance';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if ($this->option('clear')) {
            $this->clearPerformanceCache();
            return 0;
        }

        $this->info('Development Performance Monitor');
        $this->info('================================');

        $this->checkMemoryUsage();
        $this->checkCachePerformance();
        $this->checkDatabasePerformance();
        $this->checkFileSystemPerformance();
        $this->showOptimizationTips();

        return 0;
    }

    /**
     * Check current memory usage.
     */
    private function checkMemoryUsage()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = ini_get('memory_limit');

        $this->info("\nMemory Usage:");
        $this->line("Current: " . $this->formatBytes($memoryUsage));
        $this->line("Peak: " . $this->formatBytes($memoryPeak));
        $this->line("Limit: " . $memoryLimit);

        if ($memoryUsage > (512 * 1024 * 1024)) { // 512MB
            $this->warn("High memory usage detected. Consider optimizing your code.");
        }
    }

    /**
     * Check cache performance.
     */
    private function checkCachePerformance()
    {
        $this->info("\nCache Performance:");
        
        $start = microtime(true);
        Cache::put('performance_test', 'test_value', 60);
        $writeTime = microtime(true) - $start;

        $start = microtime(true);
        $value = Cache::get('performance_test');
        $readTime = microtime(true) - $start;

        $this->line("Cache Driver: " . config('cache.default'));
        $this->line("Write Time: " . number_format($writeTime * 1000, 2) . "ms");
        $this->line("Read Time: " . number_format($readTime * 1000, 2) . "ms");

        if ($writeTime > 0.01 || $readTime > 0.01) {
            $this->warn("Slow cache performance. Consider using array or redis cache for development.");
        }

        Cache::forget('performance_test');
    }

    /**
     * Check database performance.
     */
    private function checkDatabasePerformance()
    {
        $this->info("\nDatabase Performance:");
        
        $start = microtime(true);
        try {
            DB::connection()->getPdo();
            $connectionTime = microtime(true) - $start;
            
            $this->line("Connection Time: " . number_format($connectionTime * 1000, 2) . "ms");
            
            if ($connectionTime > 0.1) {
                $this->warn("Slow database connection. Check your database configuration.");
            }
        } catch (\Exception $e) {
            $this->error("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Check file system performance.
     */
    private function checkFileSystemPerformance()
    {
        $this->info("\nFile System Performance:");
        
        $testFile = storage_path('framework/cache/performance_test.txt');
        
        $start = microtime(true);
        file_put_contents($testFile, 'performance test');
        $writeTime = microtime(true) - $start;

        $start = microtime(true);
        $content = file_get_contents($testFile);
        $readTime = microtime(true) - $start;

        $this->line("File Write Time: " . number_format($writeTime * 1000, 2) . "ms");
        $this->line("File Read Time: " . number_format($readTime * 1000, 2) . "ms");

        if ($writeTime > 0.01 || $readTime > 0.01) {
            $this->warn("Slow file system performance. Consider using SSD or optimizing file operations.");
        }

        @unlink($testFile);
    }

    /**
     * Show optimization tips.
     */
    private function showOptimizationTips()
    {
        $this->info("\nOptimization Tips:");
        $this->line("• Use 'php artisan config:cache' for production");
        $this->line("• Use 'php artisan route:cache' for production");
        $this->line("• Use 'php artisan view:cache' for production");
        $this->line("• Consider using Redis for caching in development");
        $this->line("• Use 'npm run dev-fast' for faster frontend builds");
        $this->line("• Run 'php artisan dev:performance --clear' to clear performance cache");
    }

    /**
     * Clear performance-related cache.
     */
    private function clearPerformanceCache()
    {
        $this->info('Clearing performance cache...');
        
        // Clear Laravel caches
        $this->call('cache:clear');
        $this->call('config:clear');
        $this->call('route:clear');
        $this->call('view:clear');
        
        // Clear compiled files
        if (file_exists(bootstrap_path('cache/config.php'))) {
            unlink(bootstrap_path('cache/config.php'));
        }
        
        if (file_exists(bootstrap_path('cache/routes.php'))) {
            unlink(bootstrap_path('cache/routes.php'));
        }
        
        $this->info('Performance cache cleared successfully!');
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
