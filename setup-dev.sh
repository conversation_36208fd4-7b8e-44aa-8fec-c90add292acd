#!/bin/bash

# Laravel Development Environment Setup Script
# This script optimizes the Laravel application for local development

echo "🚀 Setting up optimized Laravel development environment..."

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

# Create development environment file if it doesn't exist
if [ ! -f ".env.development" ]; then
    echo "📝 Creating .env.development file..."
    cp .env.development .env.development.backup 2>/dev/null || true
else
    echo "✅ .env.development already exists"
fi

# Copy development environment for local use
echo "📋 Setting up local development environment..."
cp .env.development .env.local 2>/dev/null || echo "⚠️  .env.local already exists"

# Clear all caches
echo "🧹 Clearing all caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Clear compiled files
echo "🗑️  Clearing compiled files..."
rm -f bootstrap/cache/config.php
rm -f bootstrap/cache/routes.php
rm -f bootstrap/cache/services.php

# Optimize autoloader
echo "⚡ Optimizing autoloader..."
composer dump-autoload --optimize

# Install/update npm dependencies
echo "📦 Installing npm dependencies..."
if [ -f "package-lock.json" ]; then
    npm ci --silent
else
    npm install --silent
fi

# Clear npm cache
echo "🧹 Clearing npm cache..."
npm cache clean --force 2>/dev/null || true

# Clear webpack cache
echo "🧹 Clearing webpack cache..."
rm -rf node_modules/.cache
rm -rf public/js/*
rm -rf public/css/*
rm -f public/mix-manifest.json

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p storage/framework/cache/data
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p bootstrap/cache
mkdir -p public/js
mkdir -p public/css

# Set proper permissions
echo "🔐 Setting proper permissions..."
chmod -R 775 storage
chmod -R 775 bootstrap/cache

# Generate application key if needed
if ! grep -q "APP_KEY=base64:" .env.local 2>/dev/null; then
    echo "🔑 Generating application key..."
    php artisan key:generate --env=local
fi

# Run initial build
echo "🏗️  Running initial development build..."
npm run dev-fast 2>/dev/null || npm run dev

# Check performance
echo "📊 Checking development performance..."
php artisan dev:performance

echo ""
echo "✅ Development environment setup complete!"
echo ""
echo "🎯 Quick Start Commands:"
echo "  • Start Laravel server: php artisan serve"
echo "  • Watch for changes: npm run watch"
echo "  • Hot reload: npm run hot"
echo "  • Performance check: php artisan dev:performance"
echo "  • Clear performance cache: php artisan dev:performance --clear"
echo ""
echo "💡 Tips:"
echo "  • Use .env.local for local development"
echo "  • Use 'npm run dev-fast' for faster builds"
echo "  • Monitor performance with 'php artisan dev:performance'"
echo "  • VSCode tasks are configured for quick development"
echo ""
echo "🚀 Happy coding!"
