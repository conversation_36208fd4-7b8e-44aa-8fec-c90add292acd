{"version": "2.0.0", "tasks": [{"label": "Laravel: Serve", "type": "shell", "command": "php", "args": ["artisan", "serve", "--host=127.0.0.1", "--port=8000"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "Laravel: <PERSON> All Cache", "type": "shell", "command": "php", "args": ["artisan", "optimize:clear"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Laravel: Run Mi<PERSON>s", "type": "shell", "command": "php", "args": ["artisan", "migrate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "NPM: Development Build", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "NPM: Watch", "type": "shell", "command": "npm", "args": ["run", "watch"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true}, {"label": "NPM: Hot Reload", "type": "shell", "command": "npm", "args": ["run", "hot"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true}, {"label": "Composer: <PERSON><PERSON><PERSON>", "type": "shell", "command": "composer", "args": ["install", "--optimize-autoloader"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Composer: Update", "type": "shell", "command": "composer", "args": ["update", "--optimize-autoloader"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}