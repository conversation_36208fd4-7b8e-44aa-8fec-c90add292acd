#!/bin/bash
#
# Copy createdb.sh.example to createdb.sh
# then uncomment then set database name and username to create you need databases
#
# example: .env POSTGRES_USER=appuser and need db name is myshop_db
# 
psql -v ON_ERROR_STOP=1 --username default --dbname default <<-EOSQL
    CREATE USER myuser WITH PASSWORD 'mypassword';
    CREATE DATABASE myshop_db;
    GRANT ALL PRIVILEGES ON DATABASE myshop_db TO myuser;
    GRANT ALL ON SCHEMA public TO myuser;
    GRANT CREATE ON SCHEMA public TO myuser;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO myuser;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO myuser;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO myuser;
EOSQL

# Additional database setup for development
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Enable UUID extension
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    
    -- Grant schema permissions to existing users
    GRANT ALL ON SCHEMA public TO PUBLIC;
    GRANT CREATE ON SCHEMA public TO PUBLIC;
    
    -- If you have specific database users, grant them permissions
    -- Replace 'your_app_user' with your actual database user
    DO \$\$
    BEGIN
        IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'your_app_user') THEN
            GRANT ALL PRIVILEGES ON SCHEMA public TO your_app_user;
            GRANT CREATE ON SCHEMA public TO your_app_user;
            ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO your_app_user;
            ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO your_app_user;
        END IF;
    END
    \$\$;
EOSQL

# Enable UUID extension for the main database
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "db_dmm" <<-EOSQL
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
EOSQL
#
# this sh script will auto run when the postgres container starts and the $DATA_PATH_HOST/postgres not found.
#
# 
# psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
#     CREATE USER db1 WITH PASSWORD 'db1';
#     CREATE DATABASE db1;
#     GRANT ALL PRIVILEGES ON DATABASE db1 TO db1;
# EOSQL
# 
# psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
#     CREATE USER db2 WITH PASSWORD 'db2';
#     CREATE DATABASE db2;
#     GRANT ALL PRIVILEGES ON DATABASE db2 TO db2;
# EOSQL
# 
# psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
#     CREATE USER db3 WITH PASSWORD 'db3';
#     CREATE DATABASE db3;
#     GRANT ALL PRIVILEGES ON DATABASE db3 TO db3;
# EOSQL
