server {
    listen 80;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root /var/www/public;
    client_max_body_size     50m;
    client_header_timeout    5m;
    client_body_timeout      5m;
    proxy_connect_timeout     5m;
    proxy_read_timeout      5m;
    proxy_send_timeout      5m;
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app_pihc:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    location ~*  \.(jpg|jpeg|png|gif|ico|css|js|webp)$ {

        expires 365d;
        #test
    }
}
