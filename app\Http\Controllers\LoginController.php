<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Validator;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class LoginController extends Controller
{
    //
    public function index(Request $request)
    {
        if (Auth::check()) {
            return redirect('/perform-realtime');
        }
        return view('login');
    }

    public function find_user($username)
    {
      if(DB::table('users')->where('username', $username)->first()){
        $id = DB::table('users')->where('username', $username)->first()->id;
        $user = User::find($id);
      }
      else{
        $user = '';
      }
      return $user;
    }

    public function single_auth($data){
      $ch = curl_init(env('SSO_URL'));
      $curlDefault = array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                'token:$2y$10$3DDqyL./M7Qn4h426rnOAux3H20.VWXE2sqO83tk6n24QDtswGwF.',  // when akses using token
            ),
        );
        // execute!
        curl_setopt_array($ch, $curlDefault);
        $html = curl_exec($ch);
        $response = json_decode($html, true);
        $err = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);
        // echo "<pre>";
        // print_r($err);
        // print_r($html);
        // print_r($response);
        // die();
        // do anything you want with your response
        return $response;
    }

    public function login_ldap($username, $password)
    {
        $server = '10.60.12.44';
        $domain = "@smig.corp";
        $cek_connect = @ldap_connect($server);
        if (!$cek_connect) {
            return false;
        } else {
            $bind = @ldap_bind($cek_connect, $username.$domain, $password);
            if (!$bind) {
                return false;
            }
            ldap_close($cek_connect);
            return true;
        }
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'username' => 'required',
            'password' => 'required',
            // Hide 'g-recaptcha-response' => 'recaptcha',
        ]);

        $credentials                  = $request->all('username', 'password');
        $emailCredentials['email']    = $request['username'];
        $emailCredentials['password'] = $request['password'];

        $user = $this->find_user($request['username']);
        $remember = $request->remember ? true : false;

        if(!$user){
          $response=[
            'not-found' => [trans("messages.user-not-found")],
          ];
          return view('/login')->withErrors($response);
        }
        elseif($user->is_sso_login == true){

            $cek_ldap = $this->login_ldap($request['username'], $request['password']);

            if(!$cek_ldap){
                $response = [
                    'username' => [trans("messages.username_not_match")],
                    'password' => [trans("messages.password_not_match")],
                ];
                return view('/login')->withErrors($response);
            } else{
                Auth::login($user, $remember);
                return redirect('/perform-realtime');
            }


            //   $data = array(
            //       'username' =>  $request['username'],
            //       'password' => $request['password'],
            //       'token' => env('SSO_TOKEN'),
            //   );
            //   $respon = $this->single_auth($data);
            //     if($respon == ''){
            //       $response=[
            //         'sso-server' => [trans("messages.sso-server-unreachable")],
            //       ];
            //       return view('/login')->withErrors($response);
            //     }
            //     elseif($respon['success']) {
            //       Auth::login($user, $remember);
            //       return redirect('/perform-realtime');
            //     }
            //     elseif(!$respon['success']){
            //       $response=[
            //         'message' => ucfirst($respon['msg']),
            //       ];
            //       return view('/login')->withErrors($response);
            //     }
            //     else {
            //       $response=[
            //         'username' => [trans("messages.username_not_match")],
            //         'password' => [trans("messages.password_not_match")],
            //       ];
            //       return view('/login')->withErrors($response);
            //     }
        }
        else{
            if (Auth::attempt($credentials, $remember)) {
              return redirect('/perform-realtime');
            } elseif (Auth::attempt($emailCredentials, $remember)) {
              return redirect('/costcenter');
            } else{
              $response=[
                'username' => [trans("messages.username_not_match")],
                'password' => [trans("messages.password_not_match")],
              ];
              return view('/login')->withErrors($response);
            }
        }
    }

    public function destroy(Request $request)
    {
        Auth::logout();
        // $request->session()->invalidate();
        $request->session()->regenerate(true);
        return redirect('/');
    }

}
