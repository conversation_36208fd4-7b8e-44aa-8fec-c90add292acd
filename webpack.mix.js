const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

// Development performance optimizations
if (mix.inProduction()) {
    mix.version();
} else {
    // Development optimizations
    mix.options({
        hmrOptions: {
            host: 'localhost',
            port: 8080,
        },
        watchOptions: {
            ignored: [
                /node_modules/,
                /vendor/,
                /storage/,
                /bootstrap\/cache/,
                /\.git/,
                /\.vscode/,
                /tests/,
                /database/
            ]
        }
    });

    // Disable source maps in development for faster builds
    mix.sourceMaps(false, 'eval-cheap-module-source-map');
}

mix.js('resources/js/app.js', 'public/js')
    .sass('resources/sass/app.scss', 'public/css')
    .options({
        processCssUrls: false, // Disable CSS URL processing for faster builds
        postCss: [
            require('autoprefixer')({
                overrideBrowserslist: ['> 1%', 'last 2 versions']
            })
        ]
    });

// Development-specific configurations
if (!mix.inProduction()) {
    mix.webpackConfig({
        devtool: 'eval-cheap-module-source-map',
        cache: {
            type: 'filesystem',
            buildDependencies: {
                config: [__filename]
            }
        },
        optimization: {
            removeAvailableModules: false,
            removeEmptyChunks: false,
            splitChunks: false,
        },
        output: {
            pathinfo: false,
        },
        resolve: {
            symlinks: false,
            cacheWithContext: false,
        },
        module: {
            unsafeCache: true,
        }
    });

    // Enable hot module replacement
    mix.browserSync({
        proxy: 'localhost:8000',
        files: [
            'app/**/*.php',
            'resources/views/**/*.php',
            'resources/js/**/*.js',
            'resources/sass/**/*.scss'
        ],
        watchOptions: {
            usePolling: false,
            interval: 1000
        }
    });
}
