<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class UploadDocumentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        // Instuksi Untuk Running Upload Document
        // 1. Create <PERSON><PERSON> di <PERSON>
        // 2. Create permision my-upload
        // 3. php artisan migrate --path=/database/migrations/2025_09_03_155929_create_t_document_table.php
        // 4. php artisan db:seed --class=UploadDocumentSeeder

        $data = [
            ['permission' => 'my-upload-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/my-upload', 'route' => 'MyUploadController@index', 'guard' => 'web'],
            ['permission' => 'my-upload-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/my-upload/list', 'route' => 'MyUploadController@datatables', 'guard' => 'web'],
            ['permission' => 'my-upload-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/my-upload/{id}', 'route' => 'MyUploadController@show', 'guard' => 'web'],
            ['permission' => 'my-upload-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/my-upload', 'route' => 'MyUploadController@store', 'guard' => 'web'],
            ['permission' => 'my-upload-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/my-upload/{id}/update', 'route' => 'MyUploadController@update', 'guard' => 'web'],
            ['permission' => 'my-upload-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/my-upload/{id}/delete', 'route' => 'MyUploadController@destroy', 'guard' => 'web'],
            ['permission' => 'my-upload-A', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/my-upload/{id}/approve', 'route' => 'MyUploadController@approve', 'guard' => 'web'],
            ['permission' => 'my-upload-A', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/my-upload/{id}/reject', 'route' => 'MyUploadController@reject', 'guard' => 'web'],
          
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
    }
}
