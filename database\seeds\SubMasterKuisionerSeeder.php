<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class SubMasterKuisionerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $data = [
            ['permission' => 'masterkuisioner-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/sub-master-kuisioner', 'route' => 'Master\SubMasterKuisionerController@index', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/sub-master-kuisioner-datatable', 'route' => 'Master\SubMasterKuisionerController@datatables', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/sub-master-kuisioner/{uuid}', 'route' => 'Master\SubMasterKuisionerController@show', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/sub-master-kuisioner', 'route' => 'Master\SubMasterKuisionerController@store', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/sub-master-kuisioner/{uuid}/delete', 'route' => 'Master\SubMasterKuisionerController@destroy', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/sub-master-kuisioner/{uuid}/update', 'route' => 'Master\SubMasterKuisionerController@update', 'guard' => 'web'],
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
        // DON'T DO , i's only for emergency or development
        // php artisan migrate:rollback --path=/database/migrations/2025_08_29_132649_create_sub_master_kuisioner_table.php
    }
}
