<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;

class MasterKuisionerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Instuksi Untuk Running Master Quisioner
        // 1. Create <PERSON><PERSON> di Development
        // 2. Create permision masterkuisioner
        // 3. php artisan migrate --path=/database/migrations/2025_08_29_084714_create_master_kuisioner_table.php
        // 4. php artisan migrate --path=/database/migrations/2025_08_29_132649_create_sub_master_kuisioner_table.php
        // 5. php artisan db:seed --class=MasterKuisionerSeeder
        // 6. php artisan db:seed --class=SubMasterKuisionerSeeder

        $data = [
            ['permission' => 'masterkuisioner-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/master-kuisioner', 'route' => 'Master\MasterKuisionerController@index', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/master-kuisioner-datatable', 'route' => 'Master\MasterKuisionerController@datatables', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/master-kuisioner/{uuid}', 'route' => 'Master\MasterKuisionerController@show', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/master-kuisioner-sub/{uuid}', 'route' => 'Master\SubMasterKuisionerController@sub', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/master-kuisioner', 'route' => 'Master\MasterKuisionerController@store', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/master-kuisioner/{uuid}/delete', 'route' => 'Master\MasterKuisionerController@destroy', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/master-kuisioner/{uuid}/update', 'route' => 'Master\MasterKuisionerController@update', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-C', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/master-kuisioner-sub', 'route' => 'Master\SubMasterKuisionerController@store', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-D', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'DELETE', 'url' => '/master-kuisioner-sub/{uuid}/delete', 'route' => 'Master\SubMasterKuisionerController@destroy', 'guard' => 'web'],
            ['permission' => 'masterkuisioner-U', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'POST', 'url' => '/master-kuisioner-sub/{uuid}/update', 'route' => 'Master\SubMasterKuisionerController@update', 'guard' => 'web'],
        ];

        foreach($data as $k_data => $v_data){
            $check = Routes::where('url', $v_data['url'])->where('method',$v_data['method'])->first();
            if(!$check){
                 Routes::create($v_data);
            }else{
                Routes::where('id',$check->id)->update($v_data);
            }
        }
        $this->command->info("Routes Seeder Success !");
        // DON'T DO , i's only for emergency or development
        // php artisan migrate:rollback --path=/database/migrations/2025_08_29_084714_create_master_kuisioner_table.php
    }
}
