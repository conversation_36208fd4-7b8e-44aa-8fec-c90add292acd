<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Development performance optimizations
        if ($this->app->environment('local')) {
            // Disable unnecessary services in development
            $this->app->bind('path.public', function() {
                return base_path('public');
            });

            // Optimize memory usage
            ini_set('memory_limit', '512M');

            // Disable opcache validation in development for faster reloads
            if (function_exists('opcache_get_status') && opcache_get_status()) {
                ini_set('opcache.validate_timestamps', '1');
                ini_set('opcache.revalidate_freq', '0');
            }
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191); // Update defaultStringLength

        // Development performance optimizations
        if ($this->app->environment('local')) {
            // Disable query logging in development to save memory
            \DB::connection()->disableQueryLog();

            // Skip host validation in local development
            return;
        }

        //prevent host header attack
        $allowed_host = array(
            '127.0.0.1:8000',
            'devoprex.sig.id',
            'www.oprex.sig.id',
            'oprex.sig.id',
            'www.oprex.sig.id',
            '**********',
            '**********',
            'localhost:8080',
            'dev-dmm.test'
        );

        //Khusus akses via browser
        if (!app()->runningInConsole() && (!isset($_SERVER['HTTP_HOST']) || !in_array($_SERVER['HTTP_HOST'], $allowed_host)))
        {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
            exit;
        }
    }
}