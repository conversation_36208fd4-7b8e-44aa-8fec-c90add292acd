APP_NAME=DMM
APP_ENV=local
APP_KEY=base64:dy/0gCmVFBVTVopcroiXo+PuL64y7U2vsNtbQiWDSGE=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

# Optimized logging for development
LOG_CHANNEL=single
LOG_LEVEL=error

# Performance optimizations - disable heavy features
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
VIEW_CACHE_ENABLED=false
CONFIG_CACHE_ENABLED=false
ROUTE_CACHE_ENABLED=false

# Fast caching for development
CACHE_DRIVER=array
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

# Database configuration
DB_CONNECTION=pgsql
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=db_dmm
DB_USERNAME=dmm_user
DB_PASSWORD=sisidmm

# Disable broadcasting and mail for development
BROADCAST_DRIVER=log
MAIL_MAILER=log

# Redis configuration (optional for development)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Disable external services for development
RECAPTCHA_SITE_KEY=YOUR_API_SITE_KEY
RECAPTCHA_SECRET_KEY=YOUR_API_SECRET_KEY

# Disable SAP features for local development
SAP_ENABLED=false
SAP_RFC_ENABLED=false

# Development-specific settings
ALLOWED_REFERER="127.0.0.1:8000;localhost:8000;dev-dmm.test"

# Mix settings for development
MIX_APP_URL="${APP_URL}"
MIX_PUSHER_APP_KEY=""
MIX_PUSHER_APP_CLUSTER=""

# Development performance settings
PHP_MEMORY_LIMIT=512M
PHP_MAX_EXECUTION_TIME=300
PHP_OPCACHE_ENABLE=1
PHP_OPCACHE_VALIDATE_TIMESTAMPS=1
PHP_OPCACHE_REVALIDATE_FREQ=0

# File watching optimizations
FILESYSTEM_DRIVER=local
FILESYSTEM_CLOUD=local

# Development database optimizations
DB_SLOW_QUERY_LOG=false
DB_GENERAL_LOG=false
