<?php

use App\Models\Routes;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SearchDocSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Instuksi Untuk Running Upload Document
        // 1. Create <PERSON><PERSON> di <PERSON>
        // 2. Create permision pencarian-dokumen
        // 3. php artisan migrate --path=/database/migrations/2025_09_03_155929_create_t_document_table.php
        // 4. php artisan db:seed --class=SearchDocSeeder

        $searchRoute = [
            ['permission' => 'pencarian-dokumen-R', 'middleware' => 'lang,authz', 'type' => 'view', 'method' => 'GET', 'url' => '/pencarian-dokumen', 'route' => 'SearchDocController@index', 'guard' => 'web'],
            ['permission' => 'pencarian-dokumen-R', 'middleware' => 'lang,authz', 'type' => 'api', 'method' => 'POST', 'url' => '/pencarian-dokumen/search', 'route' => 'SearchDocController@search', 'guard' => 'web'],
        ];

        $existingRoute = Routes::where('url', $searchRoute['url'])
                              ->where('method', $searchRoute['method'])
                              ->first();

        if (!$existingRoute) {
            try {
                // Get the next available ID
                $maxId = DB::table('routes')->max('id');
                $nextId = $maxId + 1;

                // Insert with specific ID
                DB::table('routes')->insert(array_merge($searchRoute, [
                    'id' => $nextId,
                    'created_at' => now(),
                    'updated_at' => now()
                ]));

                // Update the sequence for PostgreSQL
                DB::statement("SELECT setval('routes_id_seq', $nextId)");

                $this->command->info("Created search route: POST /pencarian-dokumen/search with ID: $nextId");
            } catch (\Exception $e) {
                $this->command->error("Failed to create search route: " . $e->getMessage());

                // Try alternative approach - just check if route works without inserting
                $this->command->info("Route may already exist in system. Testing functionality...");
            }
        } else {
            $this->command->info("Search route already exists");
        }

        $this->command->info("Routes Seeder Success !");
    }
}
